// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
// Multi-Stream Test Program for P2P Socket Library

#include <string>
#include <vector>
#include <iostream>
#include <chrono>
#include <thread>
#include <cstring>
#include <map>
#include <atomic>
#include <mutex>
#include <algorithm>
#include "p2psocket.h"

// Test scenarios
enum TEST_SCENARIO {
    SCENARIO_SINGLE_STREAM = 1,     // Single stream baseline test
    SCENARIO_MULTI_STREAM = 2,      // Multi-stream concurrent test
    SCENARIO_PRIORITY_TEST = 3,     // Priority test
    SCENARIO_CALLBACK_MODE = 4,     // Callback mode test
    SCENARIO_POLL_MODE = 5,         // Poll mode test
    SCENARIO_MIXED_MODE = 6,        // Mixed mode test
};

// Data types for different streams
enum DATA_TYPE {
    DATA_CONTROL = 1,               // Control messages (small packets, high priority)
    DATA_BULK = 2,                  // Bulk data (large packets, medium priority)
    DATA_LOG = 3,                   // Log data (unidirectional, low priority)
    DATA_HEARTBEAT = 4,             // Heartbeat packets (small, high frequency)
};

// Stream configuration
struct StreamConfig {
    int stream_count = 4;           // Number of streams (1-10)
    int unidirectional_ratio = 25;  // Percentage of unidirectional streams (0-100%)
    int priority_levels = 3;        // Priority levels (1-5)
    int buffer_size = 64 * 1024;    // Buffer size
    bool use_callback = false;      // Whether to use callback mode
    int data_size_per_stream = 100; // Data size per stream (MB)
    int test_duration = 30;         // Test duration in seconds
};

// Stream statistics
struct StreamStats {
    std::atomic<uint64_t> bytes_sent{0};
    std::atomic<uint64_t> bytes_received{0};
    std::atomic<uint64_t> packets_sent{0};
    std::atomic<uint64_t> packets_received{0};
    uint64_t start_time = 0;
    uint64_t end_time = 0;
    std::atomic<double> avg_latency{0.0};
    std::atomic<int> error_count{0};
    DATA_TYPE data_type = DATA_BULK;
    int priority = 0;
    bool is_unidirectional = false;
};

// Stream information
struct StreamInfo {
    P2P_STREAM stream = nullptr;
    StreamStats stats;
    std::thread worker_thread;
    std::atomic<bool> should_stop{false};
};

// Global test configuration
StreamConfig g_config;
std::vector<StreamInfo> g_streams;
std::mutex g_stats_mutex;
std::atomic<bool> g_test_running{false};

// Logging utility
class LLog {
public:
    static void Log(const char* message) {
        std::cout << "[" << GetTimestamp() << "] " << message << std::endl;
    }

    static void Log(int level, const std::string& message) {
        std::cout << "[" << GetTimestamp() << "][" << level << "] " << message << std::endl;
    }

    template<typename... Args>
    static void Log(int level, const char* format, Args... args) {
        char buffer[1024];
        snprintf(buffer, sizeof(buffer), format, args...);
        std::cout << "[" << GetTimestamp() << "][" << level << "] " << buffer << std::endl;
    }

private:
    static std::string GetTimestamp() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        char buffer[100];
        std::strftime(buffer, sizeof(buffer), "%H:%M:%S", std::localtime(&time_t));
        return std::string(buffer) + "." + std::to_string(ms.count());
    }
};

// Utility functions
uint64_t GetCurrentTimeMS() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

// Certificate and key data (same as testcapability.cpp)
static std::string server_key =
    "-----BEGIN PRIVATE KEY-----"
    "\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCalSDHlHgpg94z\nxxVQIr"
    "Dvfzt+DngpIG4ikaW1pKWBx/+6RIZglw2vwvATEOu6kbjExd2GLmGwGBu/\n+hR7owvU/"
    "pMnoCnidX36y3CckDvhAuTLyNiQFp11SGI5wi69RpK6bA5yLLmPBQO8\n4lH9FVtyOmtjzWKnS"
    "jx+Ta2bHkuFS69VxzeF660LKIRx9V3lF67Llt+"
    "iueNnvpE7\nKNAW2VZIGsKvzsrI92ITrhPZ1qII3DKX+ApyjFXFANP+"
    "E7OAOOhGckh0J8ZjAzzI\ny1ZVyJQ2MheS4wRxrelVXrr+1BTYo+VX9vsvfGJ/pvtKGnLf/"
    "BuAMZZMwzTni8/P\n1NJJDRfHAgMBAAECggEABLx8/"
    "KB37g20STHYXVQyx6PGVt5qoBf2R+jegupAh/"
    "Fw\nzGw9EciPCsuWP39NObItTxog53OUWqjicMdgyUj9m9ERAS3PGrku2vhQwvtIWmSU\nTgoh"
    "WIjY2jcVi19/"
    "vxzicgPNjYIBTXiB3M+yY2a8HovbrnskgqurntMSdqY++"
    "h6D\nUzCF5YVTFHhPpjqRl0dcGXQexKr8+Xz4TZKwDgWdBZ+su/U4xCL0FJC/"
    "nMNZZzio\nPd8pBYboQ7hA5Be0jv/ZRs/"
    "qfPG6zTbghrO24wjEHL2e0eXuZOMNW9n52TUptiIB\ncXEygchkfAwo5aBqX/"
    "Ky+YnFRG9d8+tnLN7RJmTrgQKBgQC8H9q/"
    "nR4Rx5M4RwZJ\nPkhm5pvxBNIInTpAcyWevTj+BTe04grerNSQXCYzX2g6HHWGX+"
    "QLvvw6zxauhDsD\nIZNog7BA45DYuPxPsRb1wZt7+"
    "GksQs7RmbcRqmyuuObmpONvCK4Ee5HngyPD4Eqq\nXuLZgJA6aY47a6FjaSE1e+"
    "K5nwKBgQDSWyuQowm0iIFk1PpSoZbBKUlwC9VJZEbk\nWHW57c/kmPs7PK/"
    "xCU2g0nP3O9ar7i2gKuU4PULfeGX9JR3147Gz8orJv/zz6xaD\nVFvzm1wercFTZ/"
    "OGHieDJ3XpMKJaqJGZkT5C0R+8txNqLn0rY+Tehqy6+"
    "AjQRKrL\nOAqO4MpA2QKBgGtghzw9ku81CeviZk0iFrNdR38PcE7oZ1poHv33JXOgSMafCvNE"
    "\nlON9JXTHLTeWDOLREto9fbyXfWvJH+HOpVPPbqfq/D8nfySBgQhvJK9i/6z6yQ/L\n/"
    "DlCHFyyP2FAlmxG+QSn/"
    "4S0TpK8EAIkvLjG8AvXOg7ihTC3zfO4LZYjAoGAIeuh\nsskXn40DkIbndrun264UsYS2+"
    "Aa7h8bb9QdsJqikmoDGvk+JvQ0ytgNoMoFNyi4g\nmFkIl5CKoa+"
    "CjUwSM7pAtxfGam7WSocn7Lh4ulm6ewCgPFhQds2+LcQx6fyUvfa1\n24BtZbj/"
    "4Hdup7iMB3YgFGY6xZrND8gRq8iD1MECgYAgO5LX1ErfKB9+OeMm5Vct\nAQQB1hFShq/"
    "wuafCTVFV4ZeJT+KFYofDjp7V9Khqp4F61XqEk0lEbjQDYAsanYSv\n0RUowVZ2xU9FW7SKD++"
    "AiQKEZXeFrmhZKmTvYItzifqSjS18bmpWZcPaDCznZ7fq\ncOF7SLbtlbLWjI+FQzyXDw==\n-"
    "----END PRIVATE KEY-----";

static std::string server_cert =
    "-----BEGIN CERTIFICATE-----"
    "\nMIIC7DCCAdQCAQEwDQYJKoZIhvcNAQELBQAwPDELMAkGA1UEBhMCQ04xDzANBgNV\nBAoMBk"
    "xlbm92bzEPMA0GA1UEAwwGTGVub3ZvMQswCQYDVQQLDAJEQzAeFw0yNDAy\nMDUwMzI1MjdaFw"
    "0yNTAyMDQwMzI1MjdaMDwxCzAJBgNVBAYTAkNOMQ8wDQYDVQQK\nDAZMZW5vdm8xDzANBgNVBA"
    "MMBkxlbm92bzELMAkGA1UECwwCREMwggEiMA0GCSqG\nSIb3DQEBAQUAA4IBDwAwggEKAoIBAQ"
    "CalSDHlHgpg94zxxVQIrDvfzt+DngpIG4i\nkaW1pKWBx/"
    "+6RIZglw2vwvATEOu6kbjExd2GLmGwGBu/+hR7owvU/"
    "pMnoCnidX36\ny3CckDvhAuTLyNiQFp11SGI5wi69RpK6bA5yLLmPBQO84lH9FVtyOmtjzWKnS"
    "jx+\nTa2bHkuFS69VxzeF660LKIRx9V3lF67Llt+"
    "iueNnvpE7KNAW2VZIGsKvzsrI92IT\nrhPZ1qII3DKX+ApyjFXFANP+"
    "E7OAOOhGckh0J8ZjAzzIy1ZVyJQ2MheS4wRxrelV\nXrr+1BTYo+VX9vsvfGJ/pvtKGnLf/"
    "BuAMZZMwzTni8/"
    "P1NJJDRfHAgMBAAEwDQYJ\nKoZIhvcNAQELBQADggEBAAFY5ybddSZ5i3n4tZieM7c8NlY9Sdw"
    "o01p3Dvsx84Ri\nK3WplaWtfYgSOB5eEgcdNosspTlU1fLuqDSN87IweucTfTVvw8gnbd5DNJG"
    "2QmkD\n8+"
    "kQs1oJEarUOMJQDuG8jxtfFNMZBiEwKzQZclObDMIGHwaujVRM7FYE2H9xP2w3\nZvIbywGMvt"
    "7lBtinA6HcGyaGzqMLFTUf4zXM1qQc3wCd/"
    "eCIbG1KkR0Ht3qPQCsX\nGKZnSfOPonz9rwmlypS1g0uABAH2VNZiJNmiKY1PEmxT2ka2rrYcA"
    "+/ZcxEG5hFr\nI4oGui1jJns/DyDY4e9jyoORhi0H8mhx9jWODdcJcl4=\n-----END "
    "CERTIFICATE-----";

static std::string cli_key =
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

static std::string cli_cert =
    "-----BEGIN CERTIFICATE-----"
    "\nMIIC7DCCAdQCAQEwDQYJKoZIhvcNAQELBQAwPDELMAkGA1UEBhMCQ04xDzANBgNV\nBAoMBk"
    "xlbm92bzEPMA0GA1UEAwwGTGVub3ZvMQswCQYDVQQLDAJEQzAeFw0yNDAy\nMDUwMzI5MDJaFw"
    "0yNTAyMDQwMzI5MDJaMDwxCzAJBgNVBAYTAkNOMQ8wDQYDVQQK\nDAZMZW5vdm8xDzANBgNVBA"
    "MMBkxlbm92bzELMAkGA1UECwwCREMwggEiMA0GCSqG\nSIb3DQEBAQUAA4IBDwAwggEKAoIBAQ"
    "DmPuSYaARmcHclnaz7HIscFq2PPU9183/0\nhrb6a4qk0EHJi2YFY8KFUnxyjI2LWVnCE/"
    "CuTODXr3aqHYJlNk9ZqLomDGk2f0JI\nQQh05JvhptZCOpgRBH/"
    "ykoJef8XPk4A53SYE0ICQcoL6NPXwCszIrp5Et5LIMmok\nydJRpEuMeQhKeIrMsDhQLD81sID"
    "uouy6i2rzUMUbc0YzWfJkwBubnGoX0ktgpB6k\nWY9SR70kVGGxDuBF91aGNLc4J4GmBMQEy9J"
    "VkM036xVb/RfGMqTx4bzshyDz4QCh\nb362EkNuDvP+n/Nd1iGc/D8Z6e7KXYJZBQ+BC/"
    "T9gm31R3DtQwYlAgMBAAEwDQYJ\nKoZIhvcNAQELBQADggEBAGkrkZpBCXLNUwFfMIbVNYTLPt"
    "yxLnFVb6L2HaFO8VKW\nVpDtcW4iqNSQswtskpKKlAZAGHWv4i88NK7oB9HNcOeokI1vyW+"
    "pKL8XlRDi3Uc1\nPTjuYOYPBjqzvD5hEDAWfPOrSunf8vJ3wjMRSjeqZ0BvaaAburGwaCAlfNI"
    "wbMR3\ndnlUAhdv9gtVdfNVIC6t3+T7WW9Uiu+rD9w0GfGmuYb23TtYLg0kuzD7HMi3a/"
    "OA\na5svHYrU1rUGy+"
    "oF3eKcIS0sJj11P3EHmEj3Gt7VUtpuVwLv3ex6cz05y0JnvL6x\n0dW98D0pi77HmLvuAQVbOo"
    "3AukaF7D66p76FoXCHCWw=\n-----END CERTIFICATE-----";

// Certificate verification callback
static bool VerifyCallback(P2P_SOCKET soc, const char* x509) {
    LLog::Log(2, "Certificate verification callback called for socket %p", soc);
    return true;
}

// Stream event callback function
void streamEventCallback(P2P_STREAM stream, struct StreamEvent* event, void* user_data) {
    StreamInfo* streamInfo = static_cast<StreamInfo*>(user_data);
    if (!streamInfo) return;

    switch (event->type) {
        case STREAM_EVENT_DATA_RECEIVED:
            streamInfo->stats.packets_received++;
            streamInfo->stats.bytes_received += event->data_received.length;
            LLog::Log(3, "Stream %p: Data received (%d bytes)", stream, event->data_received.length);
            break;

        case STREAM_EVENT_SEND_COMPLETE:
            streamInfo->stats.packets_sent++;
            streamInfo->stats.bytes_sent += event->send_complete.bytes_sent;
            LLog::Log(3, "Stream %p: Send complete (%d bytes)", stream, event->send_complete.bytes_sent);
            break;

        case STREAM_EVENT_PEER_CLOSED:
            LLog::Log(2, "Stream %p: Peer closed", stream);
            streamInfo->should_stop = true;
            break;

        case STREAM_EVENT_ERROR:
            LLog::Log(1, "Stream %p: Error occurred (code: %d)", stream, event->error.error_code);
            streamInfo->stats.error_count++;
            break;
    }
}

// Multi-Stream Server Class
class MultiStreamServer {
private:
    P2P_SOCKET listen_socket = nullptr;
    P2P_SOCKET client_socket = nullptr;
    std::vector<StreamInfo> streams;

public:
    int Initialize(const StreamConfig& config) {
        LLog::Log("Initializing Multi-Stream Server...");

        // Create socket options
        SocketOptions option = {};
        option.mode = MODE_SERVER;
        option.cert_verify = VerifyCallback;
        option.cert = server_cert.c_str();
        option.privatekey = server_key.c_str();
        option.wokernum = 8;
        option.type = SOCKET_QUIC;
        option.log_level = P2P_LOG_INFO;
        option.log_path = "d:\\multistream_server_log.txt";

        // Create and configure socket
        listen_socket = P2pCreate(&option);
        if (!listen_socket) {
            LLog::Log(0, "Failed to create server socket");
            return -1;
        }

        P2pSetConnTimeout(listen_socket, 10 * 1000);
        P2pSetSendMode(listen_socket, 1);  // Direct send mode
        P2pSetReadMode(listen_socket, 0);  // Buffer read mode

        // Bind and listen
        int result = P2pBind(listen_socket, "0.0.0.0", 4433);
        if (result != 0) {
            LLog::Log(0, "Failed to bind socket: %d", result);
            return -1;
        }

        result = P2pListen(listen_socket);
        if (result != 0) {
            LLog::Log(0, "Failed to listen: %d", result);
            return -1;
        }

        LLog::Log("Server listening on port 4433...");
        return 0;
    }

    int AcceptClient() {
        LLog::Log("Waiting for client connection...");

        char client_ip[128] = {0};
        int client_port = 0;
        client_socket = P2pAccept(listen_socket, client_ip, sizeof(client_ip), &client_port);

        if (!client_socket) {
            LLog::Log(0, "Failed to accept client");
            return -1;
        }

        LLog::Log(3,"Client connected from %s:%d", client_ip, client_port);
        return 0;
    }

    int CreateStreams(const StreamConfig& config) {
        LLog::Log(3,"Creating %d streams...", config.stream_count);

        streams.resize(config.stream_count);

        for (int i = 0; i < config.stream_count; i++) {
            // Configure stream options
            struct StreamOptions streamOpts = {};
            streamOpts.unidirectional = (i * 100 / config.stream_count) < config.unidirectional_ratio ? 1 : 0;
            streamOpts.priority = (i % config.priority_levels) * (255 / config.priority_levels);
            streamOpts.buffer_size = config.buffer_size;

            // Create stream
            streams[i].stream = P2pStreamCreate(client_socket, &streamOpts);
            if (!streams[i].stream) {
                LLog::Log(0, "Failed to create stream %d", i);
                return -1;
            }

            // Set stream properties
            streams[i].stats.priority = streamOpts.priority;
            streams[i].stats.is_unidirectional = streamOpts.unidirectional;
            streams[i].stats.data_type = static_cast<DATA_TYPE>((i % 4) + 1);

            // Set callback if enabled
            if (config.use_callback) {
                P2pStreamSetCallback(streams[i].stream, streamEventCallback, &streams[i]);
            }

            LLog::Log(2, "Created stream %d: priority=%d, unidirectional=%d",
                     i, streamOpts.priority, streamOpts.unidirectional);
        }

        LLog::Log(3,"Successfully created %d streams", config.stream_count);
        return 0;
    }

    void RunReceiveTest(const StreamConfig& config) {
        LLog::Log("Starting receive test...");
        g_test_running = true;

        uint64_t start_time = GetCurrentTimeMS();
        uint64_t last_report_time = start_time;

        // Start worker threads for each stream
        for (size_t i = 0; i < streams.size(); i++) {
            streams[i].stats.start_time = start_time;
            streams[i].worker_thread = std::thread([this, i, &config]() {
                this->ReceiveWorker(i, config);
            });
        }

        // Main monitoring loop
        while (g_test_running) {
            std::this_thread::sleep_for(std::chrono::seconds(5));

            uint64_t current_time = GetCurrentTimeMS();
            if (current_time - last_report_time >= 5000) {  // Report every 5 seconds
                PrintIntermediateStats();
                last_report_time = current_time;
            }

            // Check if test duration exceeded
            if (config.test_duration > 0 &&
                (current_time - start_time) >= config.test_duration * 1000) {
                LLog::Log("Test duration reached, stopping...");
                g_test_running = false;
            }
        }

        // Wait for all worker threads to finish
        for (auto& streamInfo : streams) {
            streamInfo.should_stop = true;
            if (streamInfo.worker_thread.joinable()) {
                streamInfo.worker_thread.join();
            }
        }

        // Record end time
        uint64_t end_time = GetCurrentTimeMS();
        for (auto& streamInfo : streams) {
            streamInfo.stats.end_time = end_time;
        }

        LLog::Log("Receive test completed");
        PrintFinalStats();
    }

private:
    void ReceiveWorker(int stream_index, const StreamConfig& config) {
        StreamInfo& streamInfo = streams[stream_index];
        char buffer[64 * 1024];

        LLog::Log(2, "Receive worker started for stream %d", stream_index);

        while (g_test_running && !streamInfo.should_stop) {
            if (config.use_callback) {
                // In callback mode, just sleep and let callbacks handle data
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            } else {
                // Poll mode - actively read data
                int bytes_read = P2pStreamRead(streamInfo.stream, buffer, sizeof(buffer));
                if (bytes_read > 0) {
                    streamInfo.stats.bytes_received += bytes_read;
                    streamInfo.stats.packets_received++;
                } else if (bytes_read < 0) {
                    streamInfo.stats.error_count++;
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                }
            }
        }

        LLog::Log(2, "Receive worker finished for stream %d", stream_index);
    }

    void PrintIntermediateStats() {
        std::lock_guard<std::mutex> lock(g_stats_mutex);

        uint64_t total_bytes = 0;
        uint64_t total_packets = 0;

        for (size_t i = 0; i < streams.size(); i++) {
            const auto& stats = streams[i].stats;
            total_bytes += stats.bytes_received;
            total_packets += stats.packets_received;

            LLog::Log(2, "Stream %zu: %lu bytes, %lu packets, %d errors",
                     i, stats.bytes_received.load(), stats.packets_received.load(),
                     stats.error_count.load());
        }

        LLog::Log(1, "Total received: %lu bytes, %lu packets", total_bytes, total_packets);
    }

    void PrintFinalStats() {
        LLog::Log("=== Final Server Statistics ===");

        uint64_t total_bytes = 0;
        uint64_t total_packets = 0;
        uint64_t total_errors = 0;

        for (size_t i = 0; i < streams.size(); i++) {
            const auto& stats = streams[i].stats;
            uint64_t duration = stats.end_time - stats.start_time;
            double throughput = duration > 0 ? (stats.bytes_received * 8.0 * 1000) / (duration * 1024 * 1024) : 0;

            total_bytes += stats.bytes_received;
            total_packets += stats.packets_received;
            total_errors += stats.error_count;

            LLog::Log(1, "Stream %zu: %lu bytes, %lu packets, %.2f Mbps, %d errors, priority=%d",
                     i, stats.bytes_received.load(), stats.packets_received.load(),
                     throughput, stats.error_count.load(), stats.priority);
        }

        uint64_t total_duration = streams.empty() ? 0 :
            (streams[0].stats.end_time - streams[0].stats.start_time);
        double total_throughput = total_duration > 0 ?
            (total_bytes * 8.0 * 1000) / (total_duration * 1024 * 1024) : 0;

        LLog::Log(1, "TOTAL: %lu bytes, %lu packets, %.2f Mbps, %lu errors",
                 total_bytes, total_packets, total_throughput, total_errors);
    }

public:
    void Cleanup() {
        // Close all streams
        for (auto& streamInfo : streams) {
            if (streamInfo.stream) {
                P2pStreamClose(streamInfo.stream);
                streamInfo.stream = nullptr;
            }
        }

        // Close sockets
        if (client_socket) {
            P2pClose(client_socket);
            client_socket = nullptr;
        }

        if (listen_socket) {
            P2pClose(listen_socket);
            listen_socket = nullptr;
        }

        LLog::Log("Server cleanup completed");
    }
};

// Multi-Stream Client Class
class MultiStreamClient {
private:
    P2P_SOCKET socket = nullptr;
    std::vector<StreamInfo> streams;

public:
    int Connect(const std::string& server_ip, const StreamConfig& config) {
        LLog::Log(3,"Connecting to server %s...", server_ip.c_str());

        // Create socket options
        SocketOptions option = {};
        option.mode = MODE_CLIENT;
        option.cert_verify = VerifyCallback;
        option.cert = cli_cert.c_str();
        option.privatekey = cli_key.c_str();
        option.wokernum = 8;
        option.type = SOCKET_QUIC;
        option.log_level = P2P_LOG_INFO;
        option.log_path = "d:\\multistream_client_log.txt";

        // Create and configure socket
        socket = P2pCreate(&option);
        if (!socket) {
            LLog::Log(0, "Failed to create client socket");
            return -1;
        }

        P2pSetSendMode(socket, 1);  // Direct send mode
        P2pSetReadMode(socket, 0);  // Buffer read mode

        // Connect to server
        int result = P2pConnect(socket, server_ip.c_str(), 4433);
        if (result < 0) {
            LLog::Log(0, "Failed to connect to server: %d", result);
            return -1;
        }

        LLog::Log("Connected to server successfully");
        return 0;
    }

    int CreateStreams(const StreamConfig& config) {
        LLog::Log(3,"Creating %d streams...", config.stream_count);

        streams.resize(config.stream_count);

        for (int i = 0; i < config.stream_count; i++) {
            // Configure stream options
            struct StreamOptions streamOpts = {};
            streamOpts.unidirectional = (i * 100 / config.stream_count) < config.unidirectional_ratio ? 1 : 0;
            streamOpts.priority = (i % config.priority_levels) * (255 / config.priority_levels);
            streamOpts.buffer_size = config.buffer_size;

            // Create stream
            streams[i].stream = P2pStreamCreate(socket, &streamOpts);
            if (!streams[i].stream) {
                LLog::Log(0, "Failed to create stream %d", i);
                return -1;
            }

            // Set stream properties
            streams[i].stats.priority = streamOpts.priority;
            streams[i].stats.is_unidirectional = streamOpts.unidirectional;
            streams[i].stats.data_type = static_cast<DATA_TYPE>((i % 4) + 1);

            // Set callback if enabled
            if (config.use_callback) {
                P2pStreamSetCallback(streams[i].stream, streamEventCallback, &streams[i]);
            }

            LLog::Log(2, "Created stream %d: priority=%d, unidirectional=%d",
                     i, streamOpts.priority, streamOpts.unidirectional);
        }

        LLog::Log("Successfully created %d streams", config.stream_count);
        return 0;
    }

    void RunSendTest(const StreamConfig& config) {
        LLog::Log("Starting send test...");
        g_test_running = true;

        uint64_t start_time = GetCurrentTimeMS();
        uint64_t last_report_time = start_time;

        // Start worker threads for each stream
        for (size_t i = 0; i < streams.size(); i++) {
            streams[i].stats.start_time = start_time;
            streams[i].worker_thread = std::thread([this, i, &config]() {
                this->SendWorker(i, config);
            });
        }

        // Main monitoring loop
        while (g_test_running) {
            std::this_thread::sleep_for(std::chrono::seconds(5));

            uint64_t current_time = GetCurrentTimeMS();
            if (current_time - last_report_time >= 5000) {  // Report every 5 seconds
                PrintIntermediateStats();
                last_report_time = current_time;
            }

            // Check if test duration exceeded
            if (config.test_duration > 0 &&
                (current_time - start_time) >= config.test_duration * 1000) {
                LLog::Log("Test duration reached, stopping...");
                g_test_running = false;
            }
        }

        // Wait for all worker threads to finish
        for (auto& streamInfo : streams) {
            streamInfo.should_stop = true;
            if (streamInfo.worker_thread.joinable()) {
                streamInfo.worker_thread.join();
            }
        }

        // Record end time
        uint64_t end_time = GetCurrentTimeMS();
        for (auto& streamInfo : streams) {
            streamInfo.stats.end_time = end_time;
        }

        LLog::Log("Send test completed");
        PrintFinalStats();
    }

private:
    void SendWorker(int stream_index, const StreamConfig& config) {
        StreamInfo& streamInfo = streams[stream_index];

        // Prepare test data based on stream type
        std::string test_data;
        int packet_size;

        switch (streamInfo.stats.data_type) {
            case DATA_CONTROL:
                packet_size = 256;  // Small control messages
                break;
            case DATA_BULK:
                packet_size = 32 * 1024;  // Large data packets
                break;
            case DATA_LOG:
                packet_size = 1024;  // Medium log messages
                break;
            case DATA_HEARTBEAT:
                packet_size = 64;   // Tiny heartbeat packets
                break;
            default:
                packet_size = 8 * 1024;
                break;
        }

        test_data.resize(packet_size);
        for (int i = 0; i < packet_size; i++) {
            test_data[i] = (char)(i % 256);
        }

        LLog::Log(2, "Send worker started for stream %d (packet_size=%d)", stream_index, packet_size);

        uint64_t target_bytes = config.data_size_per_stream * 1024 * 1024;  // Convert MB to bytes
        uint64_t bytes_sent = 0;

        while (g_test_running && !streamInfo.should_stop && bytes_sent < target_bytes) {
            int result = P2pStreamWrite(streamInfo.stream, test_data.c_str(), packet_size);
            if (result > 0) {
                streamInfo.stats.bytes_sent += result;
                streamInfo.stats.packets_sent++;
                bytes_sent += result;
            } else {
                streamInfo.stats.error_count++;
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }

            // Add small delay for heartbeat streams to simulate periodic sending
            if (streamInfo.stats.data_type == DATA_HEARTBEAT) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        LLog::Log(2, "Send worker finished for stream %d (sent %lu bytes)", stream_index, bytes_sent);
    }

    void PrintIntermediateStats() {
        std::lock_guard<std::mutex> lock(g_stats_mutex);

        uint64_t total_bytes = 0;
        uint64_t total_packets = 0;

        for (size_t i = 0; i < streams.size(); i++) {
            const auto& stats = streams[i].stats;
            total_bytes += stats.bytes_sent;
            total_packets += stats.packets_sent;

            LLog::Log(2, "Stream %zu: %lu bytes, %lu packets, %d errors",
                     i, stats.bytes_sent.load(), stats.packets_sent.load(),
                     stats.error_count.load());
        }

        LLog::Log(1, "Total sent: %lu bytes, %lu packets", total_bytes, total_packets);
    }

    void PrintFinalStats() {
        LLog::Log("=== Final Client Statistics ===");

        uint64_t total_bytes = 0;
        uint64_t total_packets = 0;
        uint64_t total_errors = 0;

        for (size_t i = 0; i < streams.size(); i++) {
            const auto& stats = streams[i].stats;
            uint64_t duration = stats.end_time - stats.start_time;
            double throughput = duration > 0 ? (stats.bytes_sent * 8.0 * 1000) / (duration * 1024 * 1024) : 0;

            total_bytes += stats.bytes_sent;
            total_packets += stats.packets_sent;
            total_errors += stats.error_count;

            LLog::Log(1, "Stream %zu: %lu bytes, %lu packets, %.2f Mbps, %d errors, priority=%d",
                     i, stats.bytes_sent.load(), stats.packets_sent.load(),
                     throughput, stats.error_count.load(), stats.priority);
        }

        uint64_t total_duration = streams.empty() ? 0 :
            (streams[0].stats.end_time - streams[0].stats.start_time);
        double total_throughput = total_duration > 0 ?
            (total_bytes * 8.0 * 1000) / (total_duration * 1024 * 1024) : 0;

        LLog::Log(1, "TOTAL: %lu bytes, %lu packets, %.2f Mbps, %lu errors",
                 total_bytes, total_packets, total_throughput, total_errors);
    }

public:
    void Cleanup() {
        // Close all streams
        for (auto& streamInfo : streams) {
            if (streamInfo.stream) {
                P2pStreamClose(streamInfo.stream);
                streamInfo.stream = nullptr;
            }
        }

        // Close socket
        if (socket) {
            P2pClose(socket);
            socket = nullptr;
        }

        LLog::Log("Client cleanup completed");
    }
};

// Parse command line arguments
void ParseArguments(int argc, char* argv[], std::string& ip, StreamConfig& config, int& scenario) {
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "-ip" && i + 1 < argc) {
            ip = argv[++i];
        } else if (arg == "-scenario" && i + 1 < argc) {
            scenario = std::stoi(argv[++i]);
        } else if (arg == "-streams" && i + 1 < argc) {
            config.stream_count = std::stoi(argv[++i]);
        } else if (arg == "-size" && i + 1 < argc) {
            config.data_size_per_stream = std::stoi(argv[++i]);
        } else if (arg == "-priority" && i + 1 < argc) {
            config.priority_levels = std::stoi(argv[++i]);
        } else if (arg == "-unidirectional" && i + 1 < argc) {
            config.unidirectional_ratio = std::stoi(argv[++i]);
        } else if (arg == "-buffer" && i + 1 < argc) {
            config.buffer_size = std::stoi(argv[++i]) * 1024;  // Convert KB to bytes
        } else if (arg == "-duration" && i + 1 < argc) {
            config.test_duration = std::stoi(argv[++i]);
        } else if (arg == "-callback") {
            config.use_callback = true;
        } else if (arg == "-poll") {
            config.use_callback = false;
        } else if (arg == "-h" || arg == "--help") {
            std::cout << "Multi-Stream Test Program Usage:\n"
                      << "  -ip <address>        Server IP address (0 for server mode)\n"
                      << "  -scenario <1-6>      Test scenario:\n"
                      << "                       1: Single stream baseline\n"
                      << "                       2: Multi-stream concurrent\n"
                      << "                       3: Priority test\n"
                      << "                       4: Callback mode test\n"
                      << "                       5: Poll mode test\n"
                      << "                       6: Mixed mode test\n"
                      << "  -streams <1-10>      Number of streams (default: 4)\n"
                      << "  -size <MB>           Data size per stream in MB (default: 100)\n"
                      << "  -priority <1-5>      Number of priority levels (default: 3)\n"
                      << "  -unidirectional <0-100> Percentage of unidirectional streams (default: 25)\n"
                      << "  -buffer <KB>         Buffer size in KB (default: 64)\n"
                      << "  -duration <seconds>  Test duration in seconds (default: 30)\n"
                      << "  -callback            Use callback mode\n"
                      << "  -poll                Use poll mode\n"
                      << "  -h, --help           Show this help message\n"
                      << "\nExamples:\n"
                      << "  Server: test_multistream -ip 0 -scenario 2 -streams 4 -callback\n"
                      << "  Client: test_multistream -ip ************* -scenario 2 -streams 4 -size 50\n";
            exit(0);
        }
    }
}

// Print test configuration
void PrintConfig(const StreamConfig& config, int scenario, bool is_server) {
    LLog::Log(3,"=== Test Configuration ===");
    LLog::Log(3,"Mode: %s", is_server ? "Server" : "Client");
    LLog::Log(3,"Scenario: %d", scenario);
    LLog::Log(3,"Stream count: %d", config.stream_count);
    LLog::Log(3,"Data size per stream: %d MB", config.data_size_per_stream);
    LLog::Log(3,"Priority levels: %d", config.priority_levels);
    LLog::Log(3,"Unidirectional ratio: %d%%", config.unidirectional_ratio);
    LLog::Log(3,"Buffer size: %d KB", config.buffer_size / 1024);
    LLog::Log(3,"Test duration: %d seconds", config.test_duration);
    LLog::Log(3,"Event mode: %s", config.use_callback ? "Callback" : "Poll");
    LLog::Log(3,"==========================");
}

// Run server mode
int RunServer(const StreamConfig& config, int scenario) {
    MultiStreamServer server;

    // Initialize server
    if (server.Initialize(config) != 0) {
        LLog::Log(0, "Failed to initialize server");
        return -1;
    }

    // Accept client connection
    if (server.AcceptClient() != 0) {
        LLog::Log(0, "Failed to accept client");
        server.Cleanup();
        return -1;
    }

    // Create streams
    if (server.CreateStreams(config) != 0) {
        LLog::Log(0, "Failed to create streams");
        server.Cleanup();
        return -1;
    }

    // Run the test based on scenario
    switch (scenario) {
        case SCENARIO_SINGLE_STREAM:
        case SCENARIO_MULTI_STREAM:
        case SCENARIO_PRIORITY_TEST:
        case SCENARIO_CALLBACK_MODE:
        case SCENARIO_POLL_MODE:
        case SCENARIO_MIXED_MODE:
            server.RunReceiveTest(config);
            break;
        default:
            LLog::Log(0, "Unknown scenario: %d", scenario);
            server.Cleanup();
            return -1;
    }

    // Cleanup
    server.Cleanup();
    return 0;
}

// Run client mode
int RunClient(const std::string& server_ip, const StreamConfig& config, int scenario) {
    MultiStreamClient client;

    // Connect to server
    if (client.Connect(server_ip, config) != 0) {
        LLog::Log(0, "Failed to connect to server");
        return -1;
    }

    // Create streams
    if (client.CreateStreams(config) != 0) {
        LLog::Log(0, "Failed to create streams");
        client.Cleanup();
        return -1;
    }

    // Run the test based on scenario
    switch (scenario) {
        case SCENARIO_SINGLE_STREAM:
        case SCENARIO_MULTI_STREAM:
        case SCENARIO_PRIORITY_TEST:
        case SCENARIO_CALLBACK_MODE:
        case SCENARIO_POLL_MODE:
        case SCENARIO_MIXED_MODE:
            client.RunSendTest(config);
            break;
        default:
            LLog::Log(0, "Unknown scenario: %d", scenario);
            client.Cleanup();
            return -1;
    }

    // Cleanup
    client.Cleanup();
    return 0;
}

// Main function
int main(int argc, char* argv[]) {
    LLog::Log("Multi-Stream Test Program Starting...");

    // Default configuration
    std::string ip = "0";  // 0 means server mode
    StreamConfig config;
    int scenario = SCENARIO_MULTI_STREAM;

    // Parse command line arguments
    ParseArguments(argc, argv, ip, config, scenario);

    // Adjust configuration based on scenario
    switch (scenario) {
        case SCENARIO_SINGLE_STREAM:
            config.stream_count = 1;
            config.use_callback = false;
            break;
        case SCENARIO_CALLBACK_MODE:
            config.use_callback = true;
            break;
        case SCENARIO_POLL_MODE:
            config.use_callback = false;
            break;
        case SCENARIO_MIXED_MODE:
            // Half streams use callback, half use poll
            config.use_callback = false;  // Will be handled per-stream
            break;
    }

    bool is_server = (ip == "0");
    PrintConfig(config, scenario, is_server);

    int result = 0;

    if (is_server) {
        result = RunServer(config, scenario);
    } else {
        result = RunClient(ip, config, scenario);
    }

    if (result == 0) {
        LLog::Log("Test completed successfully!");
    } else {
        LLog::Log(0, "Test failed with error code: %d", result);
    }

    return result;
}
